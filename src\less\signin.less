@import './mixin.less';

.page-wrapper.signin {
  .flex(row, center, center);
  width: 100%;
  height: 100%;
  background-color: white;

  > .page-container {
    width: 360px;
    max-width: 100%;
    padding: 16px;
    margin-top: -64px;

    > .page-header-container {
      .flex(row, space-between, center);
      width: 100%;
      margin-bottom: 16px;

      > .title-text {
        font-size: 24px;
      }
    }

    > .page-content-container {
      .flex(column, flex-start, flex-start);
      flex-wrap: nowrap;

      > .form-item-container {
        .flex(column, flex-start, flex-start);
        position: relative;
        width: 100%;
        line-height: 1.6;
        margin-top: 8px;

        > .normal-text {
          position: absolute;
          top: 18px;
          left: 12px;
          flex-shrink: 0;
          font-size: 13px;
          line-height: 2;
          color: gray;
          cursor: text;
          padding: 0 4px;
          background-color: transparent;
          border-radius: 50%;
          transition: all 0.3s ease;

          &.not-null {
            top: -6px;
            left: 12px;
            background-color: white;
          }
        }

        &.input-form-container {
          padding: 8px 0;

          > input {
            width: 100%;
            padding: 8px 12px;
            font-size: 15px;
            line-height: 2;
            border-radius: 8px;
            border: 1px solid lightgray;
          }
        }

        &:hover {
          opacity: 0.8;
        }
      }
    }

    > .page-footer-container {
      .flex(row, space-between, center);
      width: 100%;
      margin: 12px 0;

      > .btns-container {
        .flex(row, flex-start, center);

        > .btn {
          padding: 0 4px;
          font-size: 13px;
          line-height: 32px;
          border-radius: 4px;

          &:hover {
            opacity: 0.8;
          }

          &.disabled {
            color: lightgray;
            cursor: not-allowed;
          }

          &.signin-btn {
            background-color: @text-green;
            color: white;
            padding: 0 12px;

            &.requesting {
              cursor: wait;
              opacity: 0.8;
            }
          }
        }

        > .btn-text {
          font-size: 13px;
        }

        > .split-text {
          color: lightgray;
          margin: 0 8px;
        }
      }
    }

    > .quickly-btns-container {
      .flex(column, flex-start, flex-start);
      width: 100%;
      margin-top: 24px;

      > .btn {
        margin-bottom: 24px;
        line-height: 40px;
        border: 2px solid lightgray;
        border-radius: 22px;
        padding: 0 16px;
        font-size: 15px;

        &:hover {
          opacity: 0.8;
        }

        &.guest-signin {
          color: @text-green;
          border-color: @text-green;
          font-weight: bold;
        }

        &.requesting {
          cursor: wait;
          opacity: 0.8;
        }
      }
    }
  }
}
