@import './mixin.less';

.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text, .theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text {
  .flex(column, flex-start, flex-start);
  width: 400px;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;

  > p {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-bottom: 4px;
    font-size: 15px;
    line-height: 24px;
    min-height: 24px;
    white-space: pre-wrap;

    > a {
      width: 400px;
      overflow: hidden;
      text-overflow: ellipsis; //文本溢出显示省略号
      white-space: nowrap; //文本不会换行
    }
  }

  .tag-span {
    display: inline-block;
    width: auto;
    padding-left: 4px;
    padding-right: 6px;
    margin-left: 4px;
    line-height: 24px;
    font-size: 13px;
    border: none;
    border-radius: 4px;
    color: @text-blue;
    background-color: @bg-light-blue;
    cursor: pointer;
    vertical-align: bottom;

    &:hover {
      background-color: @text-blue;
      color: white;
    }
  }

  .memo-link-text {
    display: inline-block;
    color: @text-blue;
    font-weight: bold;
    border-bottom: none;
    text-decoration: none;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .counter-block,
  .todo-block {
    display: inline-block;
    text-align: center;
    width: 1.4rem;
    .mono-font-family();
  }

  pre {
    width: 100%;
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 15px;
    line-height: 1.5;
    background-color: #f6f5f4;
    white-space: pre-wrap;
  }
}

.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text {
  .flex(column, flex-start, flex-start);
  width: 100%;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;

  > p {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-bottom: 4px;
    margin-top: -2px;
    font-size: 15px;
    line-height: 24px;
    min-height: 24px;
    white-space: pre-wrap;
  }

  .tag-span {
    display: inline-block;
    width: auto;
    padding-left: 4px;
    padding-right: 6px;
    margin-left: 4px;
    line-height: 24px;
    font-size: 13px;
    border: none;
    border-radius: 4px;
    color: @text-blue;
    background-color: @bg-light-blue;
    cursor: pointer;
    vertical-align: bottom;

    &:hover {
      background-color: @text-blue;
      color: white;
    }
  }

  .memo-link-text {
    display: inline-block;
    color: @text-blue;
    font-weight: bold;
    border-bottom: none;
    text-decoration: none;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .counter-block,
  .todo-block {
    display: inline-block;
    text-align: center;
    width: 1.4rem;
    .mono-font-family();
  }

  pre {
    width: 100%;
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 15px;
    line-height: 1.5;
    background-color: #f6f5f4;
    white-space: pre-wrap;
  }
}

//@media only screen and (max-width: 875px) {
//  .theme-light {
//    div[data-type='memos_view'] .memo-content-text {
//      > p {
//        font-size: 15px;
//        line-height: 26px;
//        min-height: 26px;
//        white-space: pre-wrap;
//      }
//
//      .tag-span {
//        line-height: 26px;
//        font-size: 14px;
//      }
//    }
//  }
//}

.theme-light {
  div[data-type='memos_view'].mobile-view .memo-content-text {
    > p {
      font-size: 15px;
      line-height: 26px;
      min-height: 26px;
      white-space: pre-wrap;
    }

    .tag-span {
      line-height: 26px;
      font-size: 14px;
    }
  }
}

.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text {
  .flex(column, flex-start, flex-start);
  width: 100%;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  color: @text-dark-black;

  > p {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-bottom: 4px;
    font-size: 15px;
    line-height: 24px;
    min-height: 24px;
    white-space: pre-wrap;
    color: @text-dark-black;
  }

  .tag-span {
    display: inline-block;
    width: auto;
    padding-left: 4px;
    padding-right: 6px;
    margin-left: 4px;
    line-height: 24px;
    font-size: 13px;
    border: none;
    border-radius: 4px;
    color: @text-dark-blue;
    background-color: @bg-dark-light-blue;
    cursor: pointer;
    vertical-align: bottom;

    &:hover {
      background-color: @text-dark-blue;
      color: rgb(0, 0, 0);
    }
  }

  .memo-link-text {
    display: inline-block;
    color: @text-dark-blue;
    font-weight: bold;
    border-bottom: none;
    text-decoration: none;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .counter-block,
  .todo-block {
    display: inline-block;
    text-align: center;
    width: 1.4rem;
    .mono-font-family();
  }

  pre {
    width: 100%;
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 15px;
    line-height: 1.5;
    background-color: #0c0c0c;
    white-space: pre-wrap;
  }
}

.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text, .theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text {
  .flex(column, flex-start, flex-start);
  width: 100%;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  color: @text-dark-black;

  > p {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-bottom: 4px;
    margin-top: -2px;
    font-size: 15px;
    line-height: 24px;
    min-height: 24px;
    white-space: pre-wrap;
    color: @text-dark-black;
  }

  .tag-span {
    display: inline-block;
    width: auto;
    padding-left: 4px;
    padding-right: 6px;
    margin-left: 4px;
    line-height: 24px;
    font-size: 13px;
    border: none;
    border-radius: 4px;
    color: @text-dark-blue;
    background-color: @bg-dark-light-blue;
    cursor: pointer;
    vertical-align: bottom;

    &:hover {
      background-color: @text-dark-blue;
      color: white;
    }
  }

  .memo-link-text {
    display: inline-block;
    color: @text-dark-blue;
    font-weight: bold;
    border-bottom: none;
    text-decoration: none;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .counter-block,
  .todo-block {
    display: inline-block;
    text-align: center;
    width: 1.4rem;
    .mono-font-family();
  }

  pre {
    width: 100%;
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 15px;
    line-height: 1.5;
    background-color: #f6f5f4;
    white-space: pre-wrap;
  }
}

//@media only screen and (max-width: 875px) {
//  .theme-dark {
//    div[data-type='memos_view'] .memolist-wrapper {
//      .memo-content-text {
//        color: @text-dark-black;
//
//        > p {
//          font-size: 15px;
//          line-height: 26px;
//          min-height: 26px;
//          white-space: pre-wrap;
//          color: @text-dark-black;
//        }
//
//        .tag-span {
//          line-height: 26px;
//          font-size: 14px;
//        }
//      }
//    }
//  }
//}

.theme-dark {
  div[data-type='memos_view'].mobile-view .memolist-wrapper {
    .memo-content-text {
      color: @text-dark-black;

      > p {
        font-size: 15px;
        line-height: 26px;
        min-height: 26px;
        white-space: pre-wrap;
        color: @text-dark-black;
      }

      .tag-span {
        line-height: 26px;
        font-size: 14px;
      }
    }
  }
}
