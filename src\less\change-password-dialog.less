@import './mixin.less';

.change-password-dialog {
  > .dialog-container {
    width: 300px;
    border-radius: 8px;

    > .dialog-content-container {
      .flex(column, flex-start, flex-start);
      width: 100%;

      > .tip-text {
        background-color: @bg-gray;
        font-size: 12px;
        padding: 8px;
        border-radius: 8px;
      }

      > .form-label {
        .flex(column, flex-start, flex-start);
        position: relative;
        width: 100%;
        line-height: 1.6;

        > .normal-text {
          position: absolute;
          left: 8px;
          padding-left: 4px;
          flex-shrink: 0;
          font-size: 13px;
          line-height: 38px;
          color: gray;
          transition: all 0.2s linear;
          cursor: text;

          &.not-null {
            top: 2px;
            left: 8px;
            background-color: white;
            font-size: 13px;
            line-height: 18px;
            padding: 0 4px;
            border-radius: 12px;
          }
        }

        &.input-form-label {
          padding: 12px 0;
          padding-bottom: 4px;

          > input {
            width: 100%;
            padding: 6px 8px;
            font-size: 13px;
            line-height: 24px;
            border-radius: 4px;
            border: 1px solid lightgray;
            background-color: transparent;
          }
        }
      }

      > .btns-container {
        .flex(row, flex-end, center);
        margin-top: 8px;
        width: 100%;

        > .btn {
          font-size: 14px;
          padding: 6px 12px;
          border-radius: 4px;
          margin-right: 8px;
          background-color: lightgray;

          &:hover {
            opacity: 0.8;
          }

          &.cancel-btn {
            background-color: unset;
          }

          &.confirm-btn {
            background-color: @text-green;
            color: white;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 875px) {
  .dialog-wrapper.change-password-dialog {
    padding: 24px 16px;
    padding-top: 64px;

    > .dialog-container {
      width: 100%;
    }
  }
}
