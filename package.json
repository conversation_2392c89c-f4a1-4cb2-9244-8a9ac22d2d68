{"name": "obsidian-memos-plus", "version": "1.0.0", "description": "An enhanced version of Memos for capturing ideas in Obsidian", "author": "Larynx", "main": "main.js", "scripts": {"lint": "eslint . --ext .ts", "dev": "npm run lint && vite build --watch --mode development", "lint:fix": "npm run lint -- -- -- fix", "prettier": "npx prettier src test --check", "prettier:fix": "npm run prettier -- -- write", "format": "npm run prettier:fix && npm run lint:fix", "build:nolint": "NODE_ENV=production rollup -c", "build": "vite build", "test": "jest", "test:watch": "yarn test -- --watch"}, "dependencies": {"obsidian": "0.16.3", "obsidian-daily-notes-interface": "^0.9.4", "react": "^17.0.2", "react-dom": "^17.0.2", "tiny-undo": "^0.0.10", "yet-another-react-lightbox": "^3.21.7"}, "devDependencies": {"@honkhonk/vite-plugin-svgr": "^1.1.0", "@jukben/emoji-search": "^2.0.1", "@popperjs/core": "^2.11.2", "@types/react": "^17.0.39", "@types/react-dom": "^17.0.11", "@types/recompose": "^0.30.10", "@types/webscopeio__react-textarea-autocomplete": "^4.7.2", "@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "@vitejs/plugin-react": "^1.2.0", "@webscopeio/react-textarea-autocomplete": "^4.9.1", "babel": "^6.23.0", "eslint": "^8.10.0", "eslint-config-prettier": "^8.4.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.2", "eslint-plugin-react-hooks": "4.6.0", "focus-trap-react": "^10.0.0", "less": "^4.1.2", "obsidian-dataview": "0.5.47", "prettier": "2.7.1", "react-popper": "^2.3.0", "react-rnd": "^10.3.5", "react-usestateref": "^1.0.8", "typescript": "^4.5.5", "vite": "^2.9.16", "vite-plugin-react-svg": "^0.2.0"}, "license": "MIT"}