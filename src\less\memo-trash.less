@import './mixin.less';
@import './memos-header.less';

.theme-light div[data-type='memos_view'] .memo-trash-wrapper {
  .flex(column, flex-start, flex-start);
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow-y: scroll;
  .hide-scroll-bar();

  > .section-header-container {
    width: 100%;
    height: 40px;
    margin-bottom: 0;

    > .title-text {
      font-weight: bold;
      font-size: 15px;
      color: @text-black;
    }
  }

  > .tip-text-container {
    width: 100%;
    height: 128px;
    .flex(column, center, center);
  }

  > .deleted-memos-container {
    .flex(column, flex-start, flex-start);
    flex-grow: 1;
    width: 100%;
    overflow-y: scroll;
    padding-bottom: 64px;
    .hide-scroll-bar();

    > .memo-wrapper {
      > .memo-content-text {
        font-size: 15px;
        line-height: 24px;
      }
    }
  }
}

//@media only screen and (max-width: 875px) {
//  .theme-light {
//    div[data-type='memos_view'] .deleted-memos-container {
//      padding: 0 12px;
//    }
//
//    div[data-type='memos_view'] .memo-trash-wrapper {
//      .flex(column, flex-start, flex-start);
//      width: 100%;
//      height: 100%;
//      flex-grow: 1;
//      overflow-y: scroll;
//      .hide-scroll-bar();
//
//      > .section-header-container {
//        width: 100%;
//        height: 58px;
//        margin-bottom: 0;
//
//        > .title-text {
//          font-weight: bold;
//          font-size: 15px;
//          color: @text-black;
//        }
//      }
//    }
//  }
//}

.theme-light {
  div[data-type='memos_view'].mobile-view .deleted-memos-container {
    padding: 0 12px;
  }

  div[data-type='memos_view'].mobile-view .memo-trash-wrapper {
    .flex(column, flex-start, flex-start);
    width: 100%;
    height: 100%;
    flex-grow: 1;
    overflow-y: scroll;
    .hide-scroll-bar();

    > .section-header-container {
      width: 100%;
      height: 58px;
      margin-bottom: 0;

      > .title-text {
        font-weight: bold;
        font-size: 15px;
        color: @text-black;
      }
    }
  }
}

.theme-dark div[data-type='memos_view'] .memo-trash-wrapper {
  .flex(column, flex-start, flex-start);
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow-y: scroll;
  .hide-scroll-bar();

  > .section-header-container {
    width: 100%;
    height: 40px;
    margin-bottom: 0;
    color: @text-dark-black;

    > .title-text {
      font-weight: bold;
      font-size: 18px;
      color: @text-dark-black;
    }
  }

  > .tip-text-container {
    width: 100%;
    height: 128px;
    .flex(column, center, center);
  }

  > .deleted-memos-container {
    .flex(column, flex-start, flex-start);
    flex-grow: 1;
    font-size: 15px;
    width: 100%;
    overflow-y: scroll;
    padding-bottom: 64px;
    color: @text-dark-black;
    .hide-scroll-bar();

    > .memo-wrapper {
      > .memo-content-text {
        font-size: 15px;
        line-height: 24px;
      }
    }
  }
}

//@media only screen and (max-width: 875px) {
//  .theme-dark {
//    div[data-type='memos_view'] .deleted-memos-container {
//      padding: 0 12px;
//    }
//
//    div[data-type='memos_view'] .memo-trash-wrapper {
//      .flex(column, flex-start, flex-start);
//      width: 100%;
//      height: 100%;
//      flex-grow: 1;
//      overflow-y: scroll;
//      .hide-scroll-bar();
//
//      > .section-header-container {
//        width: 100%;
//        height: 58px;
//        margin-bottom: 0;
//
//        > .title-text {
//          font-weight: bold;
//          font-size: 15px;
//          color: @text-dark-black;
//        }
//      }
//    }
//  }
//}

.theme-dark {
  div[data-type='memos_view'].mobile-view .deleted-memos-container {
    padding: 0 12px;
  }

  div[data-type='memos_view'].mobile-view .memo-trash-wrapper {
    .flex(column, flex-start, flex-start);
    width: 100%;
    height: 100%;
    flex-grow: 1;
    overflow-y: scroll;
    .hide-scroll-bar();

    > .section-header-container {
      width: 100%;
      height: 58px;
      margin-bottom: 0;

      > .title-text {
        font-weight: bold;
        font-size: 15px;
        color: @text-dark-black;
      }
    }
  }
}

.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper {
  .flex(column, flex-start, flex-start);
  width: 100%;
  padding: 12px 18px;
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  border: 1px solid transparent;

  &:hover {
    border-color: @bg-gray;
  }

  > .memo-top-wrapper {
    .flex(row, space-between, center);
    width: 100%;
    height: 24px;
    margin-bottom: 0;

    > .time-text {
      font-size: 12px;
      line-height: 24px;
      color: rgb(168, 168, 168);
      flex-shrink: 0;
      cursor: pointer;
    }

    > .btns-container {
      .flex(row, flex-end, center);
      position: relative;
      flex-shrink: 0;

      > .more-action-btns-wrapper {
        .flex(column, flex-start, center);
        position: absolute;
        flex-wrap: nowrap;
        top: calc(100% - 14px);
        right: -16px;
        width: auto;
        height: auto;
        padding: 12px;
        z-index: 1;
        display: none;

        &:hover {
          display: flex;
        }

        > .more-action-btns-container {
          width: 112px;
          height: auto;
          line-height: 18px;
          padding: 4px;
          white-space: nowrap;
          border-radius: 8px;
          background-color: white;
          box-shadow: 0 0 8px 0 rgb(0 0 0 / 20%);
          z-index: 1;

          > .btn {
            width: 100%;
            padding: 8px 0;
            padding-left: 24px;
            border-radius: 4px;
            height: unset;
            line-height: unset;
            justify-content: flex-start;


            &.delete-btn {
              color: @text-red;

              &.final-confirm {
                font-weight: bold;
              }
            }
          }
        }
      }

      .btn {
        .flex(row, center, center);
        width: 100%;
        height: 28px;
        font-size: 13px;
        border-radius: 4px;

        &:hover {
          background-color: @bg-whitegray;
        }

        &.more-action-btn {
          width: 28px;
          cursor: unset;
          margin-right: -6px;
          opacity: 0.8;

          > .icon-img {
            width: 16px;
            height: 16px;

          }

          &:hover {
            background-color: unset;

            & + .more-action-btns-wrapper {
              display: flex;
            }
          }
        }
      }
    }
  }

  > .memo-content-text {
    width: 100%;
  }

  > .images-wrapper {
    .flex(row, flex-start, flex-start);
    margin-top: 8px;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 4px;
    .pretty-scroll-bar(0, 2px);

    > .memo-img {
      margin-right: 8px;
      width: auto;
      height: 128px;
      flex-shrink: 0;
      flex-grow: 0;
      overflow-y: hidden;
      .hide-scroll-bar();

      &:hover {
        border-color: lightgray;
      }

      &:last-child {
        margin-right: 0;
      }

      > img {
        width: auto;
        max-height: 128px;
        border-radius: 8px;
      }
    }
  }
}

.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper {
  .flex(column, flex-start, flex-start);
  width: 100%;
  padding: 12px 18px;
  background-color: #303030;
  border-radius: 8px;
  border: 1px solid transparent;

  &:hover {
    border-color: @bg-dark-gray;
  }

  > .memo-top-wrapper {
    .flex(row, space-between, center);
    width: 100%;
    height: 24px;
    margin-bottom: 0;

    > .time-text {
      font-size: 12px;
      line-height: 24px;
      color: rgb(214, 214, 214);
      flex-shrink: 0;
      cursor: pointer;
    }

    > .btns-container {
      .flex(row, flex-end, center);
      position: relative;
      flex-shrink: 0;

      > .more-action-btns-wrapper {
        .flex(column, flex-start, center);
        position: absolute;
        flex-wrap: nowrap;
        top: calc(100% - 14px);
        right: -16px;
        width: auto;
        height: auto;
        padding: 12px;
        z-index: 1;
        display: none;

        &:hover {
          display: flex;
        }

        > .more-action-btns-container {
          width: 112px;
          height: auto;
          line-height: 18px;
          padding: 4px;
          white-space: nowrap;
          border-radius: 8px;
          background-color: #181818;
          // box-shadow: 0 0 8px 0 rgba(219, 219, 219, 0.2);
          z-index: 1;

          > .btn {
            width: 100%;
            padding: 8px 0;
            padding-left: 24px;
            border-radius: 4px;
            height: unset;
            line-height: unset;
            justify-content: flex-start;
            color: @text-dark-black;

            &.delete-btn {
              color: @text-dark-red;

              &.final-confirm {
                font-weight: bold;
              }
            }
          }
        }
      }

      .btn {
        .flex(row, center, center);
        width: 100%;
        height: 28px;
        font-size: 13px;
        border-radius: 4px;

        &:hover {
          background-color: @bg-dark-whitegray;
        }

        &.more-action-btn {
          width: 28px;
          cursor: unset;
          margin-right: -6px;
          opacity: 0.8;

          > .icon-img {
            width: 16px;
            height: 16px;
            fill: #cdcdcd;
          }

          &:hover {
            background-color: unset;

            & + .more-action-btns-wrapper {
              display: flex;
            }
          }
        }
      }
    }
  }

  > .memo-content-text {
    width: 100%;
  }

  > .images-wrapper {
    .flex(row, flex-start, flex-start);
    margin-top: 8px;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 4px;
    .pretty-scroll-bar(0, 2px);

    > .memo-img {
      margin-right: 8px;
      width: auto;
      height: 128px;
      flex-shrink: 0;
      flex-grow: 0;
      overflow-y: hidden;
      .hide-scroll-bar();

      &:hover {
        border-color: rgb(68, 68, 68);
      }

      &:last-child {
        margin-right: 0;
      }

      > img {
        width: auto;
        max-height: 128px;
        border-radius: 8px;
      }
    }
  }
}
