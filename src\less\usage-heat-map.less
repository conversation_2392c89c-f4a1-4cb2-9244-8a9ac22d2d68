@import './mixin.less';

@stat-day-L1-bg: #9be9a8;
@stat-day-L2-bg: #40c463;
@stat-day-L3-bg: #30a14e;
@stat-day-L4-bg: #216e39;

@stat-dark-day-L1-bg: #f75205;
@stat-dark-day-L2-bg: #e03a07;
@stat-dark-day-L3-bg: #bf2104;
@stat-dark-day-L4-bg: #940b01;

.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper {
  .flex(row, flex-start, center);
  width: 100%;
  height: 122px;
  flex-wrap: wrap;
  padding-right: 24px;
  padding-bottom: 12px;

  &:hover {
    > .day-tip-text-container {
      visibility: visible;
    }
  }

  > .day-tip-text-container {
    .flex(column, space-between, center);
    width: 24px;
    height: 100%;
    padding-bottom: 2px;
    flex-wrap: wrap;
    visibility: hidden;

    > .tip-text {
      font-size: 10px;
      line-height: 16px;
      padding-right: 2px;
      width: 100%;
      text-align: right;
      color: gray;
      .mono-font-family();
    }
  }

  > .usage-heat-map {
    width: 192px;
    height: 100%;
    flex-wrap: wrap;
    display: grid;
    grid-template-rows: repeat(7, 1fr);
    grid-template-columns: repeat(12, 1fr);
    grid-auto-flow: column;

    > .stat-container {
      display: block;
      width: 13px;
      height: 13px;
      background-color: @bg-lightgray;
      border-radius: 2px;
      margin-bottom: 2px;

      &.null {
        background-color: transparent;
      }

      &.stat-day-L1-bg {
        background-color: @stat-day-L1-bg;
      }

      &.stat-day-L2-bg {
        background-color: @stat-day-L2-bg;
      }

      &.stat-day-L3-bg {
        background-color: @stat-day-L3-bg;
      }

      &.stat-day-L4-bg {
        background-color: @stat-day-L4-bg;
      }

      &.today {
        border: 1px solid black;
      }
    }
  }

  > .usage-detail-container {
    position: absolute;
    left: 0;
    top: 0;
    margin-left: 9px;
    transform: translateX(-50%);
    margin-top: -36px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.6;
    z-index: 2;
    user-select: none;
    white-space: nowrap;

    > .date-text {
      color: lightgray;
    }

    &::before {
      content: '';
      position: absolute;
      bottom: -4px;
      left: calc(50% - 6px);
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 4px solid rgba(0, 0, 0, 0.8);
    }
  }
}

@media only screen and (max-width: 875px) {
  .theme-light div[data-type='memos_view'] .usage-heat-map-wrapper {
    height: 160px;
    padding: 8px 0 !important;
    padding-top: 12px !important;

    > .day-tip-text-container {
      visibility: visible;
      width: 48px;
      padding-bottom: 4px;

      > .tip-text {
        padding-right: 6px;
        font-size: 12px;
        line-height: unset !important;
      }
    }

    > .usage-heat-map {
      width: 240px;

      > .stat-container {
        width: 16px;
        height: 16px;
        margin-bottom: 4px;
      }
    }

    > .usage-detail-container {
      margin-top: -32px;
      margin-left: 16px;
      font-size: 10px;

      &::before {
        left: calc(50% - 4px);
      }
    }
  }
}

.theme-light div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper {
  height: 160px;
  padding: 8px 0 !important;
  padding-top: 12px !important;

  > .day-tip-text-container {
    visibility: visible;
    width: 48px;
    padding-bottom: 4px;

    > .tip-text {
      padding-right: 6px;
      font-size: 12px;
      line-height: unset !important;
    }
  }

  > .usage-heat-map {
    width: 240px;

    > .stat-container {
      width: 16px;
      height: 16px;
      margin-bottom: 4px;
    }
  }

  > .usage-detail-container {
    margin-top: -32px;
    margin-left: 16px;
    font-size: 10px;

    &::before {
      left: calc(50% - 4px);
    }
  }
}

.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper {
  .flex(row, flex-start, center);
  width: 100%;
  height: 122px;
  flex-wrap: wrap;
  padding-right: 24px;
  padding-bottom: 12px;

  &:hover {
    > .day-tip-text-container {
      visibility: visible;
    }
  }

  > .day-tip-text-container {
    .flex(column, space-between, center);
    width: 24px;
    height: 100%;
    padding-bottom: 2px;
    flex-wrap: wrap;
    visibility: hidden;

    > .tip-text {
      font-size: 10px;
      line-height: 16px;
      padding-right: 2px;
      width: 100%;
      text-align: right;
      color: gray;
      .mono-font-family();
    }
  }

  > .usage-heat-map {
    width: 192px;
    height: 100%;
    flex-wrap: wrap;
    display: grid;
    grid-template-rows: repeat(7, 1fr);
    grid-template-columns: repeat(12, 1fr);
    grid-auto-flow: column;

    > .stat-container {
      display: block;
      width: 13px;
      height: 13px;
      background-color: #d8d8d8;
      border-radius: 2px;
      margin-bottom: 2px;

      &.null {
        background-color: transparent;
      }

      &.stat-day-L1-bg {
        background-color: @stat-dark-day-L1-bg;
      }

      &.stat-day-L2-bg {
        background-color: @stat-dark-day-L2-bg;
      }

      &.stat-day-L3-bg {
        background-color: @stat-dark-day-L3-bg;
      }

      &.stat-day-L4-bg {
        background-color: @stat-dark-day-L4-bg;
      }

      &.today {
        border: 1px solid rgb(255, 255, 255);
      }
    }
  }

  > .usage-detail-container {
    position: absolute;
    left: 0;
    top: 0;
    margin-left: 9px;
    transform: translateX(-50%);
    margin-top: -36px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.6;
    z-index: 2;
    user-select: none;
    white-space: nowrap;

    > .date-text {
      color: lightgray;
    }

    &::before {
      content: '';
      position: absolute;
      bottom: -4px;
      left: calc(50% - 6px);
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 4px solid rgba(0, 0, 0, 0.8);
    }
  }
}

@media only screen and (max-width: 875px) {
  .theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper {
    height: 160px;
    padding: 8px 0 !important;
    padding-top: 12px !important;

    > .day-tip-text-container {
      visibility: visible;
      width: 48px;
      padding-bottom: 4px;

      > .tip-text {
        padding-right: 6px;
        font-size: 12px;
        line-height: unset !important;
      }
    }

    > .usage-heat-map {
      width: 240px;

      > .stat-container {
        width: 16px;
        height: 16px;
        margin-bottom: 4px;
      }
    }

    > .usage-detail-container {
      margin-top: -32px;
      margin-left: 16px;
      font-size: 10px;

      &::before {
        left: calc(50% - 4px);
      }
    }
  }
}

.theme-dark div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper {
  height: 160px;
  padding: 8px 0 !important;
  padding-top: 12px !important;

  > .day-tip-text-container {
    visibility: visible;
    width: 48px;
    padding-bottom: 4px;

    > .tip-text {
      padding-right: 6px;
      font-size: 12px;
      line-height: unset !important;
    }
  }

  > .usage-heat-map {
    width: 240px;

    > .stat-container {
      width: 16px;
      height: 16px;
      margin-bottom: 4px;
    }
  }

  > .usage-detail-container {
    margin-top: -32px;
    margin-left: 16px;
    font-size: 10px;

    &::before {
      left: calc(50% - 4px);
    }
  }
}
