@import './mixin.less';

.preview-image-dialog {
  padding: 0;

  .dialog-container {
    width: 100%;
    height: 100%;
    padding: 0;
    background-color: transparent;
  }

  .preview-dialog-lightbox {
    position: absolute;
    inset: 0;
  }

  .copy-button {
    position: fixed;
    right: 30px;
    bottom: 30px;
    z-index: 2000;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

.yarl__root {
  --yarl__color_backdrop: transparent;
  --yarl__color_button: #fff;
  --yarl__color_button_active: #fff;
  --yarl__color_button_hover: rgba(255, 255, 255, 0.8);
  --yarl__slide_title_color: #fff;

  .yarl__button {
    padding: 8px;
    margin: 0 4px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    transition: background-color 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  .yarl__toolbar {
    gap: 12px;
    padding: 20px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), transparent);
  }
}

.theme-dark {
  .preview-image-dialog {
    .copy-button {
      background-color: rgba(44, 44, 44, 0.8);
      color: #cdcdcd;

      &:hover {
        background-color: rgba(68, 68, 68, 0.8);
      }
    }
  }

  .yarl__root {
    --yarl__color_button: #cdcdcd;
    --yarl__color_button_active: #cdcdcd;
    --yarl__slide_title_color: #cdcdcd;

    .yarl__button {
      background: rgba(44, 44, 44, 0.8);

      &:hover {
        background: rgba(68, 68, 68, 0.8);
      }
    }

    .yarl__toolbar {
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
    }
  }
}
