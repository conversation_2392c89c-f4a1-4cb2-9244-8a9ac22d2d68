@import './mixin.less';

.theme-light .rta {
  position: relative;
  font-size: 15px;
  width: 100%;
  height: 100%;
  z-index: 10;

  > ::-webkit-scrollbar {
    width: 2px;
    height: 16px;
    background-color: #f5f5f5;
  }

  > ::-webkit-scrollbar-track {
    //    -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.3);
    //    border-radius:10px;
    background-color: #f5f5f5;
  }

  ::-webkit-scrollbar-thumb {
    //    border-radius:10px;
    //    -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,.3);
    background-color: #555;
  }

  ::-webkit-scrollbar-track-piece {
    background-color: rgb(255, 255, 255);
  }
}

.theme-light .rta__loader.rta__loader--empty-suggestion-data {
  display: none;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(27, 31, 35, 0.1);
  padding: 5px;
}
.theme-light .rta--loading .rta__loader.rta__loader--suggestion-data {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
}
.theme-light .rta--loading .rta__loader.rta__loader--suggestion-data > * {
  display: none;
  position: relative;
  top: 50%;
}
.theme-light .rta__textarea {
  width: 100%;
  height: 100%;
  font-size: 1em;
}
.theme-light .rta__autocomplete {
  position: absolute;
  display: block;
  margin-top: 1em;
}
.theme-light .rta__autocomplete--top {
  margin-top: 0;
  margin-bottom: 1em;
}
.theme-light .rta__list {
  margin: 0;
  padding: 0;
  background: #fff;
  border: 1px solid #dfe2e5;
  border-radius: 0px;
  box-shadow: 0 0 10px rgba(27, 31, 35, 0.1);
  list-style: none;
}
.theme-light .rta__entity {
  background: white;
  width: 100%;
  text-align: left;
  outline: none;
}
.theme-light .rta__entity:hover {
  cursor: pointer;
}
.theme-light .rta__item {
  text-overflow: 'ellipsis';
  line-height: 30px;
}
.theme-light .rta__item:fisrt-child {
  border-radius: 8px 8px 0px 0px;
}
.theme-light .rta__item:not(:last-child) {
  border-bottom: 1px solid #f3f3f3;
}
.theme-light .rta__item:last-child {
  border-radius: 0px 0px 0px 0px;
}
.theme-light .rta__entity > * {
  padding-left: 4px;
  padding-right: 4px;
}
.theme-light .rta__entity--selected {
  color: #fff;
  text-decoration: none;
  background: #82af48;
}

.theme-dark .rta {
  position: relative;
  font-size: 15px;
  width: 100%;
  height: 100%;
  z-index: 10;

  > ::-webkit-scrollbar {
    width: 2px;
    height: 16px;
    background-color: #0f0f0f;
  }

  > ::-webkit-scrollbar-track {
    //    -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.3);
    //    border-radius:10px;
    background-color: #000000;
  }

  ::-webkit-scrollbar-thumb {
    //    border-radius:10px;
    //    -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,.3);
    background-color: rgb(201, 201, 201);
  }

  ::-webkit-scrollbar-track-piece {
    background-color: rgb(0, 0, 0);
  }
}

.theme-dark .rta__loader.rta__loader--empty-suggestion-data {
  display: none;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(27, 31, 35, 0.1);
  padding: 5px;
}
.theme-dark .rta--loading .rta__loader.rta__loader--suggestion-data {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
}
.theme-dark .rta--loading .rta__loader.rta__loader--suggestion-data > * {
  display: none;
  position: relative;
  top: 50%;
}
.theme-dark .rta__textarea {
  width: 100%;
  height: 100%;
  font-size: 1em;
}
.theme-dark .rta__autocomplete {
  position: absolute;
  display: block;
  margin-top: 1em;
}
.theme-dark .rta__autocomplete--top {
  margin-top: 0;
  margin-bottom: 1em;
}
.theme-dark .rta__list {
  margin: 0;
  padding: 0;
  background: rgb(255, 255, 255);
  border: 1px solid #474747;
  border-radius: 0px;
  // box-shadow: 0 0 10px rgba(27, 31, 35, 0.1);
  list-style: none;
}
.theme-dark .rta__entity {
  color: @text-dark-black;
  background: rgb(0, 0, 0);
  width: 100%;
  text-align: left;
  outline: none;
}
.theme-dark .rta__entity:hover {
  cursor: pointer;
}
.theme-dark .rta__item {
  text-overflow: 'ellipsis';
  line-height: 30px;
}
.theme-dark .rta__item:fisrt-child {
  border-radius: 8px 8px 0px 0px;
}
.theme-dark .rta__item:not(:last-child) {
  border-bottom: 1px solid #141414;
}
.theme-dark .rta__item:last-child {
  border-radius: 0px 0px 0px 0px;
}
.theme-dark .rta__entity > * {
  padding-left: 4px;
  padding-right: 4px;
}
.theme-dark .rta__entity--selected {
  color: rgb(0, 0, 0);
  text-decoration: none;
  background: #af487b;
}

//@media only screen and (max-width: 875px) {
//  .theme-light {
//    .rta__textarea {
//      max-height: 140px;
//      overflow-y: scroll;
//    }
//  }
//
//  .theme-dark {
//    .rta__textarea {
//      max-height: 140px;
//      overflow-y: scroll;
//    }
//  }
//}

.theme-light div[data-type='memos_view'].mobile-view {
  .rta__textarea {
    max-height: 140px;
    overflow-y: scroll;
  }
}

.theme-dark div[data-type='memos_view'].mobile-view {
  .rta__textarea {
    max-height: 140px;
    overflow-y: scroll;
  }
}
