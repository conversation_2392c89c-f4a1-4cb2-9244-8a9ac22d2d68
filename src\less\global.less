@import './mixin.less';

// ⚠️ This font is only free for personal use but not for commercial.
@font-face {
  font-family: 'DINPro';
  // src: url("/fonts/DINPro-Regular.otf");
  src: '';
  font-weight: normal;
}

@font-face {
  font-family: 'DINPro';
  // src: url("/fonts/DINPro-Bold.otf");
  src: '';
  font-weight: bold;
}

@font-face {
  font-family: 'ubuntu-mono';
  // src: url("/fonts/UbuntuMono.ttf");
  src: '';
  font-style: normal;
}

div[data-type='memos_view']  {
  font-family: var(--font-interface), -apple-system, BlinkMacSystemFont, "PingFang SC", "Noto Sans", "Noto Sans CJK SC", "Microsoft YaHei UI", "Microsoft YaHei",
  "WenQuanYi Micro Hei", sans-serif, "Segoe UI", <PERSON>o, "Helvetica Neue", <PERSON><PERSON>, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
  "Noto Color Emoji";
  /*text-align: var();*/
  -webkit-font-smoothing: subpixel-antialiased;
}

div[data-type='memos_view'] .view-content:not(.images-wrapper) img {
  max-width: 100%;
  cursor: default;
}

.theme-light div[data-type='memos_view'] {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color: @text-black;
  -webkit-tap-highlight-color: transparent;
}

.theme-light div[data-type='memos_view'] body,
.theme-light div[data-type='memos_view'] html {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 15px;
}

.theme-light div[data-type='memos_view'] code {
  .mono-font-family();
  background-color: pink;
  padding: 2px 4px;
  border-radius: 4px;
}

.theme-light div[data-type='memos_view'] pre {
  .mono-font-family();

  * {
    .mono-font-family();
  }
}

.theme-light div[data-type='memos_view'] label,
.theme-light div[data-type='memos_view'] input,
.theme-light div[data-type='memos_view'] button:not(.rdp),
.theme-light div[data-type='memos_view'] textarea,
.theme-light div[data-type='memos_view'] img {
  background-color: transparent;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  border: none;
  outline: none;
}

.theme-light div[data-type='memos_view'] input:-webkit-autofill,
.theme-light div[data-type='memos_view'] input:-webkit-autofill:hover,
.theme-light div[data-type='memos_view'] input:-webkit-autofill:focus,
.theme-light div[data-type='memos_view'] input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px white inset !important;
}

.theme-light div[data-type='memos_view'] li:not(.rta__item) {
  list-style-type: none;

  &::before {
    content: '•';
    font-weight: bold;
    margin-right: 4px;
  }
}

.theme-light div[data-type='memos_view'] a {
  cursor: pointer;
  color: @text-blue;
  text-underline-offset: 2px;

  &:hover {
    background-color: @bg-gray;
  }
}

.theme-light div[data-type='memos_view'] a.is-unresolved {
  cursor: pointer;
  color: @text-unresolved-blue;
  text-underline-offset: 2px;

  &:hover {
    background-color: @bg-gray;
  }
}

.theme-light div[data-type='memos_view'] .btn {
  border: unset;
  background-color: unset;
  font-size: unset;
  user-select: none;
  cursor: pointer;
  text-align: center;
}

.theme-light .hidden {
  display: none !important;
}

@media only screen and (max-width: 875px) {
  .theme-light div[data-type='memos_view'] body,
  .theme-light div[data-type='memos_view'] html {
    -webkit-overflow-scrolling: touch;
  }
}

.theme-light div[data-type='memos_view'].mobile-view body,
.theme-light div[data-type='memos_view'].mobile-view html {
  -webkit-overflow-scrolling: touch;
}

.theme-dark div[data-type='memos_view'] {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color: @text-black;
  -webkit-tap-highlight-color: transparent;
}

// .theme-dark .side-dock-ribbon-action[aria-label='Memos'] {
//   filter: invert(1);
// }

.theme-dark div[data-type='memos_view'] body,
.theme-dark div[data-type='memos_view'] html {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 15px;
}

.theme-dark div[data-type='memos_view'] code {
  .mono-font-family();
  background-color: rgb(168, 168, 168);
  padding: 2px 4px;
  border-radius: 4px;
}

.theme-dark div[data-type='memos_view'] pre {
  .mono-font-family();

  * {
    .mono-font-family();
  }
}

.theme-dark div[data-type='memos_view'] label,
.theme-dark div[data-type='memos_view'] input,
.theme-dark div[data-type='memos_view'] button,
.theme-dark div[data-type='memos_view'] textarea,
.theme-dark div[data-type='memos_view'] img {
  background-color: transparent;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  border: none;
  outline: none;
}

.theme-dark div[data-type='memos_view'] input:-webkit-autofill,
.theme-dark div[data-type='memos_view'] input:-webkit-autofill:hover,
.theme-dark div[data-type='memos_view'] input:-webkit-autofill:focus,
.theme-dark div[data-type='memos_view'] input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px rgb(0, 0, 0) inset !important;
}

.theme-dark div[data-type='memos_view'] li:not(.rta__item) {
  list-style-type: none;

  &::before {
    content: '•';
    font-weight: bold;
    margin-right: 4px;
  }
}

.theme-dark div[data-type='memos_view'] a {
  cursor: pointer;
  color: @text-blue;
  text-underline-offset: 2px;

  &:hover {
    background-color: @bg-dark-blue;
  }
}

.theme-dark div[data-type='memos_view'] a.is-unresolved {
  cursor: pointer;
  color: @text-unresolved-blue;
  text-underline-offset: 2px;

  &:hover {
    background-color: @bg-dark-gray;
  }
}

.theme-dark div[data-type='memos_view'] .btn {
  border: unset;
  background-color: unset;
  font-size: unset;
  user-select: none;
  cursor: pointer;
  text-align: center;
}

.theme-dark .hidden {
  display: none !important;
}

//@media only screen and (max-width: 875px) {
//  .theme-dark div[data-type='memos_view'] body,
//  .theme-dark div[data-type='memos_view'] html {
//    -webkit-overflow-scrolling: touch;
//  }
//}


.theme-dark div[data-type='memos_view'].mobile-view body,
.theme-dark div[data-type='memos_view'].mobile-view html {
  -webkit-overflow-scrolling: touch;
}

.theme-dark div[data-type='memos_view'] svg {
  fill: #cdcdcd;
}

.theme-dark .dialog-wrapper {
  .btn-group, .btns-container {
    //svg{
      fill: #cdcdcd;
    //}
  }

}
