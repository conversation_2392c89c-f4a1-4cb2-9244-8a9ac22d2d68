// English

export default {
  // setting.ts
  welcome: 'Welcome to the Memos',
  ribbonIconTitle: 'Memos',
  to: 'to',
  months: [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
  monthsShort: ['Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'June', 'July', 'Aug.', 'Sept.', 'Oct.', 'Nov.', 'Dec.'],
  weekDays: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
  weekDaysShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
  year: null,
  month: null,
  'Basic Options': 'Basic Options',
  'User name in Memos': 'User name in Memos',
  "Set your user name here. 'Memos 😏' By default": "Set your user name here. 'Memos 😏' By default",
  'Insert after heading': 'Insert after heading',
  'You should set the same heading below if you want to insert and process memos below the same heading.':
    'You should set the same heading below if you want to insert and process memos below the same heading.',
  'Allows admonitions to be created using ': 'Allows admonitions to be created using ',
  'Process Memos below': 'Process Memos below',
  'Only entries below this string/section in your notes will be processed. If it does not exist no notes will be processed for that file.':
    'Only entries below this string/section in your notes will be processed. If it does not exist no notes will be processed for that file.',
  'Save Memo button label': 'Save Memo button label',
  "The text shown on the save Memo button in the UI. 'NOTEIT' by default.":
    "The text shown on the save Memo button in the UI. 'NOTEIT' by default.",
  'Focus on editor when open memos': 'Focus on editor when open memos',
  'Focus on editor when open memos. Focus by default.': 'Focus on editor when open memos. Focus by default.',
  'Open daily memos with open memos': 'Open daily memos with open memos',
  'Open daily memos with open memos. Open by default.': 'Open daily memos with open memos. Open by default.',
  'Open Memos when obsidian opens': 'Open Memos when obsidian opens',
  'When enable this, Memos will open when Obsidian opens. False by default.':
    'When enable this, Memos will open when Obsidian opens. False by default.',
  'Hide done tasks in Memo list': 'Hide done tasks in Memo list',
  'Hide all done tasks in Memo list. Show done tasks by default.':
    'Hide all done tasks in Memo list. Show done tasks by default.',
  'Advanced Options': 'Advanced Options',
  'UI language for date': 'UI language for date',
  "Translates the date UI language. Only 'en' and 'zh' are available.":
    "Translates the date UI language. Only 'en' and 'zh' are available.",
  'Default prefix': 'Default prefix',
  "Set the default prefix when create memo, 'List' by default.":
    "Set the default prefix when create memo, 'List' by default.",
  'Default insert date format': 'Default insert date format',
  "Set the default date format when insert date by @, 'Tasks' by default.":
    "Set the default date format when insert date by @, 'Tasks' by default.",
  'Default editor position on mobile': 'Default editor position on mobile',
  "Set the default editor position on Mobile, 'Top' by default.":
    "Set the default editor position on Mobile, 'Top' by default.",
  'Use button to show editor on mobile': 'Use button to show editor on mobile',
  'Set a float button to call editor on mobile. Only when editor located at the bottom works.':
    'Set a float button to call editor on mobile. Only when editor located at the bottom works.',
  'Show Time When Copy Results': 'Show Time When Copy Results',
  'Show time when you copy results, like 12:00. Copy time by default.':
    'Show time when you copy results, like 12:00. Copy time by default.',
  'Show Date When Copy Results': 'Show Date When Copy Results',
  'Show date when you copy results, like [[2022-01-01]]. Copy date by default.':
    'Show date when you copy results, like [[2022-01-01]]. Copy date by default.',
  'Add Blank Line Between Different Date': 'Add Blank Line Between Different Date',
  'Add blank line when copy result with date. No blank line by default.':
    'Add blank line when copy result with date. No blank line by default.',
  'Share Options': 'Share Options',
  'Share Memos Image Footer Start': 'Share Memos Image Footer Start',
  "Set anything you want here, use {MemosNum} to display Number of memos, {UsedDay} for days. '{MemosNum} Memos {UsedDay} Days' By default":
    "Set anything you want here, use {MemosNum} to display Number of memos, {UsedDay} for days. '{MemosNum} Memos {UsedDay} Days' By default",
  'Share Memos Image Footer End': 'Share Memos Image Footer End',
  "Set anything you want here, use {UserName} as your username. '✍️ By {UserName}' By default":
    "Set anything you want here, use {UserName} as your username. '✍️ By {UserName}' By default",
  'Save Shared Image To Folder For Mobile': 'Save Shared Image To Folder For Mobile',
  'Save image to folder for mobile. False by Default': 'Save image to folder for mobile. False by Default',
  'Say Thank You': 'Say Thank You',
  Donate: 'Donate',
  'If you like this plugin, consider donating to support continued development:':
    'If you like this plugin, consider donating to support continued development:',
  'File Name of Recycle Bin': 'File Name of Recycle Bin',
  "Set the filename for recycle bin. 'delete' By default": "Set the filename for recycle bin. 'delete' By default",
  'File Name of Query File': 'File Name of Query File',
  "Set the filename for query file. 'query' By default": "Set the filename for query file. 'query' By default",
  'Use Tags In Vault': 'Use Tags In Vault',
  'Use tags in vault rather than only in Memos. False by default.':
    'Use tags in vault rather than only in Memos. False by default.',
  'Ready to convert image into background': 'Ready to convert image into background',
  List: 'List',
  Task: 'Task',
  Top: 'Top',
  Bottom: 'Bottom',
  TAG: 'TAG',
  DAY: 'DAY',
  QUERY: 'QUERY',
  EDIT: 'EDIT',
  PIN: 'PIN',
  UNPIN: 'UNPIN',
  DELETE: 'DELETE',
  'CONFIRM！': 'CONFIRM！',
  'CREATE FILTER': 'CREATE FILTER',
  'Comment it...': 'Comment it...',
  Settings: 'Settings',
  'Recycle bin': 'Recycle bin',
  'About Me': 'About Me',
  'Fetching data...': 'Fetching data...',
  'Here is No Zettels.': 'Here is No Zettels.',
  'Frequently Used Tags': 'Frequently Used Tags',
  'What do you think now...': 'What do you think now...',
  READ: 'READ',
  MARK: 'MARK',
  SHARE: 'SHARE',
  SOURCE: 'SOURCE',
  RESTORE: 'RESTORE',
  'DELETE AT': 'DELETE AT',
  'Noooop!': 'Noooop!',
  'All Data is Loaded 🎉': 'All Data is Loaded 🎉',
  'Quick filter': 'Quick filter',
  TYPE: 'TYPE',
  LINKED: 'LINKED',
  'NO TAGS': 'NO TAGS',
  'HAS LINKS': 'HAS LINKS',
  'HAS IMAGES': 'HAS IMAGES',
  INCLUDE: 'INCLUDE',
  EXCLUDE: 'EXCLUDE',
  TEXT: 'TEXT',
  IS: 'IS',
  ISNOT: 'ISNOT',
  SELECT: 'SELECT',
  'ADD FILTER TERMS': 'ADD FILTER TERMS',
  FILTER: 'FILTER',
  TITLE: 'TITLE',
  'CREATE QUERY': 'CREATE QUERY',
  'EDIT QUERY': 'EDIT QUERY',
  MATCH: 'MATCH',
  TIMES: 'TIMES',
  'Share Memo Image': 'Share Memo Image',
  '↗Click the button to save': '↗Click the button to save',
  'Image is generating...': 'Image is generating...',
  'Image is loading...': 'Image is loading...',
  'Loading...': 'Loading...',
  '😟 Cannot load image, image link maybe broken': '😟 Cannot load image, image link maybe broken',
  'Daily Memos': 'Daily Memos',
  'CANCEL EDIT': 'CANCEL EDIT',
  'LINK TO THE': 'LINK TO THE',
  'Mobile Options': 'Mobile Options',
  'Experimental Options': 'Experimental Options',
  "Don't support web image yet, please input image path in vault":
    "Don't support web image yet, please input image path in vault",
  'Background Image in Dark Theme': 'Background Image in Dark Theme',
  'Background Image in Light Theme': 'Background Image in Light Theme',
  'Set background image in dark theme. Set something like "Daily/one.png"':
    'Set background image in dark theme. Set something like "Daily/one.png"',
  'Set background image in light theme. Set something like "Daily/one.png"':
    'Set background image in light theme. Set something like "Daily/one.png"',
  'Set default memo composition, you should use {TIME} as "HH:mm" and {CONTENT} as content. "{TIME} {CONTENT}" by default':
    'Set default memo composition, you should use {TIME} as "HH:mm" and {CONTENT} as content. "{TIME} {CONTENT}" by default',
  'Default Memo Composition': 'Default Memo Composition',
  'Show Tasks Label': 'Show Tasks Label',
  'Show tasks label near the time text. False by default': 'Show tasks label near the time text. False by default',
  'Please Open Memos First': 'Please Open Memos First',
  DATE: 'DATE',
  OBSIDIAN_NLDATES_PLUGIN_NOT_ENABLED: 'OBSIDIAN_NLDATES_PLUGIN_NOT_ENABLED',
  BEFORE: 'BEFORE',
  AFTER: 'AFTER',
  'Allow Comments On Memos': 'Allow Comments On Memos',
  'You can comment on memos. False by default': 'You can comment on memos. False by default',
  Import: 'Import',
  'TITLE CANNOT BE NULL!': 'TITLE CANNOT BE NULL!',
  'FILTER CANNOT BE NULL!': 'FILTER CANNOT BE NULL!',
  'Comments In Original DailyNotes/Notes': 'Comments In Original DailyNotes/Notes',
  'You should install Dataview Plugin ver 0.5.9 or later to use this feature.':
    'You should install Dataview Plugin ver 0.5.9 or later to use this feature.',
  'Open Memos Successfully': 'Open Memos Successfully',
  'Fetch Error': '😭 Fetch Error',
  'Copied to clipboard Successfully': 'Copied to clipboard Successfully',
  'Check if you opened Daily Notes Plugin Or Periodic Notes Plugin':
    'Check if you opened Daily Notes Plugin Or Periodic Notes Plugin',
  'Please finish the last filter setting first': 'Please finish the last filter setting first',
  'Close Memos Successfully': 'Close Memos Successfully',
  'Insert as Memo': 'Insert as Memo',
  'Insert file as memo content': 'Insert file as memo content',
  'Image load failed': 'Image load failed',
  'Content cannot be empty': 'Content cannot be empty',
  'Unable to create new file.': 'Unable to create new file.',
  'Failed to fetch deleted memos: ': 'Failed to fetch deleted memos: ',
  'RESTORE SUCCEED': 'RESTORE SUCCEED',
  'Save Memo button icon': 'Save Memo button icon',
  'The icon shown on the save Memo button in the UI.': 'The icon shown on the save Memo button in the UI.',
  'Fetch Memos From Particular Notes': 'Fetch Memos From Particular Notes',
  'You can set any Dataview Query for memos to fetch it. All memos in those notes will show on list. "#memo" by default':
    'You can set any Dataview Query for memos to fetch it. All memos in those notes will show on list. "#memo" by default',
  'Allow Memos to Fetch Memo from Notes': 'Allow Memos to Fetch Memo from Notes',
  'Use Memos to manage all memos in your notes, not only in daily notes. False by default':
    'Use Memos to manage all memos in your notes, not only in daily notes. False by default',
  'Always Show Memo Comments': 'Always Show Memo Comments',
  'Always show memo comments on memos. False by default': 'Always show memo comments on memos. False by default',
  "You didn't set folder for daily notes in both periodic-notes and daily-notes plugins.":
    "You didn't set folder for daily notes in both periodic-notes and daily-notes plugins.",
  'Please check your daily note plugin OR periodic notes plugin settings':
    'Please check your daily note plugin OR periodic notes plugin settings',
  "Use Which Plugin's Default Configuration": "Use Which Plugin's Default Configuration",
  "Memos use the plugin's default configuration to fetch memos from daily, 'Daily' by default.":
    "Memos use the plugin's default configuration to fetch memos from daily, 'Daily' by default.",
  Daily: 'Daily',
  'Always Show Leaf Sidebar on PC': 'Always Show Leaf Sidebar on PC',
  'Show left sidebar on PC even when the leaf width is less than 875px. False by default.':
    'Show left sidebar on PC even when the leaf width is less than 875px. False by default.',
  "You didn't set format for daily notes in both periodic-notes and daily-notes plugins.":
    "You didn't set format for daily notes in both periodic-notes and daily-notes plugins.",
};
