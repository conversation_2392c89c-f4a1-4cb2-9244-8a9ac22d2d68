@import './mixin.less';

.theme-light div[data-type='memos_view'] .menu-btns-popup {
  .flex(column, flex-start, flex-start);
  position: absolute;
  margin-top: 4px;
  margin-left: 90px;
  padding: 4px;
  width: 180px;
  border-radius: 8px;
  z-index: 20;
  box-shadow: 0 0 8px 0 rgb(0 0 0 / 20%);
  background-color: white;

  &:hover {
    display: flex;
  }

  > .btn {
    .flex(row, flex-start, center);
    width: 100%;
    padding: 8px 4px;
    font-size: 14px;
    line-height: 1.6;
    border-radius: 4px;
    text-align: left;

    > .icon {
      display: block;
      width: 28px;
      text-align: center;
      margin-right: 4px;
      font-size: 14px;
    }

    &:hover {
      background-color: @bg-whitegray;
    }
  }
}

//@media only screen and (max-width: 875px) {
//  div[data-type='memos_view'] .menu-btns-popup {
//    margin-left: 64px;
//  }
//}

div[data-type='memos_view'].mobile-view .menu-btns-popup {
  margin-left: 64px;
}

.theme-dark div[data-type='memos_view'] .menu-btns-popup {
  .flex(column, flex-start, flex-start);
  position: absolute;
  margin-top: 4px;
  margin-left: 90px;
  padding: 4px;
  width: 180px;
  border-radius: 8px;
  z-index: 20;
  // box-shadow: 0 0 8px 0 rgba(255, 255, 255, 0.2);
  background-color: rgb(0, 0, 0);

  &:hover {
    display: flex;
  }

  > .btn {
    .flex(row, flex-start, center);
    width: 100%;
    padding: 8px 4px;
    font-size: 14px;
    line-height: 1.6;
    border-radius: 4px;
    text-align: left;

    > .icon {
      display: block;
      width: 28px;
      text-align: center;
      margin-right: 4px;
      font-size: 14px;
    }

    &:hover {
      background-color: @bg-dark-whitegray;
    }
  }
}

//@media only screen and (max-width: 875px) {
//  .theme-dark {
//    div[data-type='memos_view'] .menu-btns-popup {
//      margin-left: 64px;
//    }
//  }
//}

.theme-dark {
  div[data-type='memos_view'].mobile-view .menu-btns-popup {
    margin-left: 64px;
  }
}
