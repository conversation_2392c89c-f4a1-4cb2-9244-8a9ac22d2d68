@import './mixin.less';

// 确保容器有足够的高度来显示滚动条
div[data-type='memos_view'] {
  display: flex;
  flex-direction: column;
  height: 100%;

  .memolist-wrapper {
    .flex(column, flex-start, flex-start);
    flex-grow: 1;
    width: 100%;
    height: 100%;
    max-height: 100vh;
    overflow-y: scroll;
    gap: 8px;
    padding-right: 4px; // 为滚动条预留空间
    
    // 基础滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 3px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
      }
    }

    // Firefox滚动条
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

    > .status-text-container {
      .flex(column, flex-start, center);
      width: 100%;
      margin-top: 16px;
      margin-bottom: 16px;

      &.completed {
        margin-bottom: 64px;
      }

      &.invisible {
        visibility: hidden;
      }

      > .status-text {
        font-size: 13px;
        color: gray;
      }
    }

    &.completed {
      padding-bottom: 80px;
    }
  }

  &.mobile-view .memolist-wrapper {
    padding: 0 12px;
  }
}

// 深色主题样式
.theme-dark {
  div[data-type='memos_view'] {
    .memolist-wrapper {
      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2);

        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      }

      // Firefox深色主题滚动条
      scrollbar-color: rgba(255, 255, 255, 0.2) transparent;

      > .status-text-container {
        > .status-text {
          color: rgb(255, 255, 255);
        }
      }
    }
  }
}
