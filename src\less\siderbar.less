@import './mixin.less';

.theme-light .memos-sidebar-wrapper {
  .flex(column, flex-start, flex-start);
  width: 240px;
  height: 100%;
  padding: 16px 0;
  overflow-x: hidden;
  overflow-y: auto;
  flex-shrink: 0;
  .hide-scroll-bar();

  > * {
    flex-shrink: 0;
  }
}

.theme-light .memos-sidebar-wrapper-display, .theme-dark .memos-sidebar-wrapper-display {
  display: none;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 240px;
  height: 100%;
  padding: 16px 0;
  overflow-x: hidden;
  overflow-y: auto;
  flex-shrink: 0;
}

//@media only screen and (max-width: 875px) {
//  body.mobile-show-sidebar {
//    #page-wrapper {
//      > .memos-sidebar-wrapper {
//        transform: translateX(0);
//        box-shadow: 0 0 8px 0 rgb(0 0 0 / 20%);
//      }
//    }
//  }
//
//  .memos-sidebar-wrapper {
//    .flex(column, flex-start, center);
//    z-index: 99;
//    position: absolute;
//    top: 0;
//    left: 0;
//    width: 320px;
//    height: 100%;
//    padding: 0;
//    background-color: white;
//    transition: all 0.4s ease;
//    transform: translateX(-320px);
//
//    > * {
//      width: 320px;
//      max-width: 95%;
//      flex-shrink: 0;
//      padding-left: 32px;
//    }
//  }
//}

.mobile-show-sidebar .mobile-view {
  #page-wrapper {
    > .memos-sidebar-wrapper {
      transform: translateX(0);
      box-shadow: 0 0 8px 0 rgb(0 0 0 / 20%);
    }
  }
}

.theme-light .mobile-view .memos-sidebar-wrapper {
  .flex(column, flex-start, center);
  z-index: 99;
  position: absolute;
  top: 0;
  left: 0;
  width: 320px;
  height: 100%;
  padding: 0;
  background-color: white;
  transition: all 0.4s ease;
  transform: translateX(-320px);

  > * {
    width: 320px;
    max-width: 95%;
    flex-shrink: 0;
    padding-left: 32px;
  }
}

.theme-dark .memos-sidebar-wrapper {
  .flex(column, flex-start, flex-start);
  width: 240px;
  height: 100%;
  padding: 16px 0;
  overflow-x: hidden;
  overflow-y: auto;
  flex-shrink: 0;
  .hide-scroll-bar();

  > * {
    flex-shrink: 0;
  }
}

//@media only screen and (max-width: 875px) {
//  .theme-dark .memos-sidebar-wrapper {
//    .flex(column, flex-start, center);
//    z-index: 99;
//    position: absolute;
//    top: 0;
//    left: 0;
//    width: 320px;
//    height: 100%;
//    padding: 0;
//    background-color: rgb(0, 0, 0);
//    transition: all 0.4s ease;
//    transform: translateX(-320px);
//
//    > * {
//      width: 320px;
//      max-width: 95%;
//      flex-shrink: 0;
//      padding-left: 32px;
//    }
//  }
//}

.theme-dark .mobile-view .memos-sidebar-wrapper {
  .flex(column, flex-start, center);
  z-index: 99;
  position: absolute;
  top: 0;
  left: 0;
  width: 320px;
  height: 100%;
  padding: 0;
  background-color: rgb(0, 0, 0);
  transition: all 0.4s ease;
  transform: translateX(-320px);

  > * {
    width: 320px;
    max-width: 95%;
    flex-shrink: 0;
    padding-left: 32px;
  }
}
