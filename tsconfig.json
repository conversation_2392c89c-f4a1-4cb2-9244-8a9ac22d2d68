{"compilerOptions": {"baseUrl": ".", "paths": {"memo-types": ["src/types/memo.d.ts"]}, "allowSyntheticDefaultImports": true, "inlineSourceMap": true, "inlineSources": true, "jsx": "react", "module": "ESNext", "target": "esnext", "allowJs": true, "strict": false, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "moduleResolution": "node", "importHelpers": true, "noEmit": false, "lib": ["dom", "esnext", "DOM.Iterable", "scripthost", "es2015"], "types": ["@honkhonk/vite-plugin-svgr/client"], "typeRoots": ["./src/types", "./node_modules/@types"]}, "include": ["**/*.ts", "**/*.tsx"]}