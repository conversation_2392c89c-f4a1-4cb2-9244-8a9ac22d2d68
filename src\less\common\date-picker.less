@import '../mixin.less';

.theme-light .date-picker-wrapper {
  .flex(column, flex-start, flex-start);
  padding: 16px;

  > .date-picker-header {
    .flex(row, center, center);
    width: 100%;

    > .btn-text {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;
      user-select: none;

      > .icon-img {
        width: 100%;
        height: auto;
      }

      &:hover {
        background-color: @bg-whitegray;
      }
    }

    > .normal-text {
      margin: 0 4px;
      line-height: 24px;
    }
  }

  > .date-picker-day-container {
    .flex(row, flex-start, flex-start);
    width: 280px;
    flex-wrap: wrap;

    > .date-picker-day-header {
      .flex(row, space-around, center);
      width: 100%;

      > .day-item {
        .flex(column, center, center);
        width: 36px;
        height: 36px;
        user-select: none;
        color: gray;
        font-size: 13px;
        margin: 2px 0;
      }
    }

    > .day-item {
      .flex(column, center, center);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      font-size: 14px;
      user-select: none;
      cursor: pointer;
      margin: 2px;

      &:hover {
        background-color: @bg-whitegray;
      }

      &.current {
        background-color: @bg-light-blue;
        font-size: 16px;
        color: @text-blue;
        font-weight: bold;
      }

      &.null {
        background-color: unset;
        cursor: unset;
      }
    }
  }
}

.theme-light .editor-date-picker {
  .flex(column, flex-start, flex-start);
  padding: unset;
  background-color: white;
  border: 1px dashed @bg-black;

  > .date-picker-header {
    .flex(row, center, center);
    width: 100%;

    > .btn-text {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;
      user-select: none;

      > .icon-img {
        width: 100%;
        height: auto;
      }

      &:hover {
        background-color: @bg-whitegray;
      }
    }

    > .normal-text {
      margin: 0 4px;
      line-height: 24px;
    }
  }

  > .date-picker-day-container {
    .flex(row, flex-start, flex-start);
    width: 280px;
    flex-wrap: wrap;

    > .date-picker-day-header {
      .flex(row, space-around, center);
      width: 100%;

      > .day-item {
        .flex(column, center, center);
        width: 36px;
        height: 36px;
        user-select: none;
        color: gray;
        font-size: 13px;
        margin: 2px 0;
      }
    }

    > .day-item {
      .flex(column, center, center);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      font-size: 14px;
      user-select: none;
      cursor: pointer;
      margin: 2px;

      &:hover {
        background-color: @bg-whitegray;
      }

      &.current {
        background-color: @bg-light-blue;
        font-size: 16px;
        color: @text-blue;
        font-weight: bold;
      }

      &.null {
        background-color: unset;
        cursor: unset;
      }
    }
  }
}

.theme-dark .date-picker-wrapper {
  .flex(column, flex-start, flex-start);
  padding: 16px;

  > .date-picker-header {
    .flex(row, center, center);
    width: 100%;

    > .btn-text {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;
      user-select: none;

      > .icon-img {
        width: 100%;
        height: auto;
        fill: #cdcdcd;
      }

      &:hover {
        background-color: @bg-dark-whitegray;
      }
    }

    > .normal-text {
      margin: 0 4px;
      line-height: 24px;
    }
  }

  > .date-picker-day-container {
    .flex(row, flex-start, flex-start);
    width: 280px;
    flex-wrap: wrap;

    > .date-picker-day-header {
      .flex(row, space-around, center);
      width: 100%;

      > .day-item {
        .flex(column, center, center);
        width: 36px;
        height: 36px;
        user-select: none;
        color: rgb(184, 184, 184);
        font-size: 13px;
        margin: 2px 0;
      }
    }

    > .day-item {
      .flex(column, center, center);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      font-size: 14px;
      user-select: none;
      cursor: pointer;
      margin: 2px;

      &:hover {
        background-color: @bg-dark-whitegray;
      }

      &.current {
        background-color: @bg-dark-light-blue;
        font-size: 16px;
        color: @text-blue;
        font-weight: bold;
      }

      &.null {
        background-color: unset;
        cursor: unset;
      }
    }
  }
}

.theme-dark .editor-date-picker {
  .flex(column, flex-start, flex-start);
  padding: unset;
  background-color: @bg-black;
  border: 1px dashed @bg-dark-black;

  > .date-picker-header {
    .flex(row, center, center);
    width: 100%;

    > .btn-text {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;
      user-select: none;

      > .icon-img {
        width: 100%;
        height: auto;
        fill: #cdcdcd;
      }

      &:hover {
        background-color: @bg-dark-whitegray;
      }
    }

    > .normal-text {
      margin: 0 4px;
      line-height: 24px;
    }
  }

  > .date-picker-day-container {
    .flex(row, flex-start, flex-start);
    width: 280px;
    flex-wrap: wrap;

    > .date-picker-day-header {
      .flex(row, space-around, center);
      width: 100%;

      > .day-item {
        .flex(column, center, center);
        width: 36px;
        height: 36px;
        user-select: none;
        color: rgb(184, 184, 184);
        font-size: 13px;
        margin: 2px 0;
      }
    }

    > .day-item {
      .flex(column, center, center);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      font-size: 14px;
      user-select: none;
      cursor: pointer;
      margin: 2px;

      &:hover {
        background-color: @bg-dark-whitegray;
      }

      &.current {
        background-color: @bg-dark-light-blue;
        font-size: 16px;
        color: @text-blue;
        font-weight: bold;
      }

      &.null {
        background-color: unset;
        cursor: unset;
      }
    }
  }
}
