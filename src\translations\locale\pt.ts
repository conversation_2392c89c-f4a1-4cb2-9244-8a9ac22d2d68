// Português

export default {
  // setting.ts
  welcome: 'Bem-vindo ao Memos!',
  ribbonIconTitle: 'Memos',
  months: [
    'Janeiro',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
  ],
  monthsShort: ['Jan.', 'Fev.', 'Mar.', 'Abr.', '<PERSON><PERSON>', 'Jun.', 'Jul.', 'Ago.', 'Set.', 'Out.', 'Nov.', 'Dez.'],
  weekDays: ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ua<PERSON>', '<PERSON>uinta', '<PERSON><PERSON>', 'S<PERSON>bad<PERSON>'],
  weekDaysShort: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],
  to: 'para',
  year: null,
  month: null,
  'Basic Options': 'Opções Básicas',
  'User name in Memos': 'Nome de Usuário no <PERSON>',
  "Set your user name here. 'Memos 😏' By default": "Defina o nome de usuário. Padrão: 'Memos 😏'.",
  'Insert after heading': 'Inserir após o cabeçalho',
  'You should set the same heading below if you want to insert and process memos below the same heading.':
    'Deve definir o mesmo cabeçalho na configuração posterior se pretende inserir e processar memorandos abaixo do cabeçalho aqui definido.',
  'Allows admonitions to be created using ': 'Permitir que Admonitions sejam criadas usando ',
  'Process Memos below': 'Processar Memorandos abaixo do Cabeçalho',
  'Only entries below this string/section in your notes will be processed. If it does not exist no notes will be processed for that file.':
    'Somente as entradas abaixo deste cabeçalho serão processadas nas suas notas. Se não configurar esta funcionalidade, nenhuma nota será processada para o ficheiro respetivo.',
  'Save Memo button label': 'Legenda do Botão de Guardar Memorandos',
  "The text shown on the save Memo button in the UI. 'NOTEIT' by default.":
    'Define o texto apresentado na UI do botão guardar memorandos. Padrão: "NOTEIT".',
  'Focus on editor when open memos': 'Focar no Editor ao iniciar o Memos',
  'Focus on Editor when open memos. Focus by default.': 'Focar no editor ao iniciar o Memos. Padrão: "Focar".',
  'Open daily memos with open memos': 'Abrir memorandos diários ao iniciar o Memos',
  'Open daily memos with open memos. Open by default.': 'Abrir memorandos diários ao iniciar o Memos. Padrão: "Abrir".',
  'Open Memos when obsidian opens': 'Abrir Memos quando o Obsidian inicia',
  'When enable this, Memos will open when Obsidian opens. False by default.':
    'Quando esta opção está activa, o Memos abrirá quando o Obsidian inicia. Padrão: "Falso".',
  'Hide done tasks in Memo list': 'Ocultar tarefas concluídas na lista de memorandos',
  'Hide all done tasks in Memo list. Show done tasks by default.':
    'Ocultar todas as tarefas concluídas na lista de memorandos. Padrão: "Mostrar tarefas concluídas".',
  'Advanced Options': 'Opções Avançadas',
  'UI language for date': 'Idioma na UI da Data ',
  "Translates the date UI language. Only 'en' and 'zh' are available.":
    "Define o idioma na UI da Data. De momento, apenas 'en', 'fr', 'pt' e 'zh' estão disponíveis.",
  'Default prefix': 'Prefixo Padrão',
  "Set the default prefix when create memo, 'List' by default.":
    "Define o prefixo padrão quando um memorando é criado. Padrão: 'Lista'.",
  'Default insert date format': 'Formato Padrão para Inserção de Data',
  "Set the default date format when insert date by @, 'Tasks' by default.":
    "Define o formato de Data padrão ao inserir a data usando '@'. Padrão: 'Tarefas'.",
  'Default editor position on mobile': 'Posição Padrão do Editor de Memorandos na Versão Móvel',
  "Set the default editor position on Mobile, 'Top' by default.":
    "Define a posição padrão do editor de memorandos na versão móvel. Padrão: 'Topo'.",
  'Use button to show editor on mobile': 'Usar Botão para Mostrar o Editor na Versão Móvel',
  'Set a float button to call editor on mobile. Only when editor located at the bottom works.':
    "Define um botão flutuante para abrir o editor na versão móvel. Opção disponível somente quando a posição do editor está definida para 'Fundo'.",
  'Show Time When Copy Results': 'Mostrar a Hora ao Copiar os Resultados',
  'Show time when you copy results, like 12:00. Copy time by default.':
    "Mostrar a Hora, no formato '12:00', ao copiar os resultados. Padrão: 'Copiar a hora'.",
  'Show Date When Copy Results': 'Mostrar a Data ao Copiar os Resultados',
  'Show date when you copy results, like [[2022-01-01]]. Copy date by default.':
    'Mostrar a Data, no formato [[2022-01-01]], ao copiar os resultados. Padrão: "Copiar a hora".',
  'Add Blank Line Between Different Date': 'Adicionar Linha em Branco entre Datas Diferentes.',
  'Add blank line when copy result with date. No blank line by default.':
    'Adicionar linha em branco ao copiar resultados com Data. Padrão: "Não adicionar linha."',
  'Share Options': 'Opções de Partilha',
  'Share Memos Image Footer Start': 'Partilhar a Imagem de um memorando - Início do Rodapé',
  "Set anything you want here, use {MemosNum} to display Number of memos, {UsedDay} for days. '{MemosNum} Memos {UsedDay} Days' By default":
    "Defina como preferir, use {MemosNum} para mostrar o número de memorandos e use {UsedDay} para dias. 'Padrão: {MemosNum} Memorandos {UsedDay} Dias'.",
  'Share Memos Image Footer End': 'Partilhar a Imagem de um memorando - Fim do Rodapé',
  "Set anything you want here, use {UserName} as your username. '✍️ By {UserName}' By default":
    "Defina como preferir, use {UserName} como o seu nome de usuário. Padrão: '✍️ Por {UserName}'.",
  'Save Shared Image To Folder For Mobile': 'Guardar a Imagem Partilhada para Pasta na Versão Móvel',
  'Save image to folder for mobile. False by Default':
    'Guardar a imagem partilhada para pasta na versão móvel. Padrão: "Falso".',
  'Say Thank You': 'Agradeça',
  Donate: 'Doar',
  'If you like this plugin, consider donating to support continued development:':
    'Se gosta deste plugin, considere doar para apoiar o seu desenvolvimento contínuo:',
  'File Name of Recycle Bin': 'Nome da Reciclagem',
  "Set the filename for recycle bin. 'delete' By default":
    "Define o nome do ficheiro para a Reciclagem. Padrão: 'delete'.",
  'File Name of Query File': 'Nome do Ficheiro de Query',
  "Set the filename for query file. 'query' By default": "Define o nome do ficheiro de Query. Padrão: 'Query'.",
  'Use Tags In Vault': 'Usar Tags no Vault',
  'Use tags in vault rather than only in Memos. False by default.':
    'Usar as Tags do Vault e não somente dos memorandos. Padrão: "Falso".',
  'Ready to convert image into background': 'Pronto para converter imagem em fundo',
  List: 'Lista',
  Task: 'Tarefa',
  Top: 'Topo',
  Bottom: 'Fundo',
  TAG: 'TAG',
  DAY: 'DIA',
  QUERY: 'QUERY',
  EDIT: 'EDITAR',
  PIN: 'FIXAR',
  UNPIN: 'DESAFIXAR',
  DELETE: 'ELIMINAR',
  'CONFIRM！': 'CONFIRMAR！',
  'CREATE FILTER': 'CRIAR FILTRO',
  Settings: 'Definições',
  'Recycle bin': 'Reciclagem',
  'About Me': 'Acerca de mim',
  'Fetching data...': 'A obter dados...',
  'Here is No Zettels.': 'Não existem Zettels.',
  'Frequently Used Tags': 'Tags Usadas Frequentemente',
  'What do you think now...': 'Em que está a pensar...',
  READ: 'LER',
  MARK: 'ASSINALAR',
  SHARE: 'PARTILHAR',
  SOURCE: 'ORIGEM',
  RESTORE: 'RESTAURAR',
  'DELETE AT': 'ELIMINADO EM',
  'Noooop!': 'Noooop!',
  'All Data is Loaded 🎉': 'Todos os Dados foram Carregados 🎉',
  'Quick filter': 'Filtro rápido',
  TYPE: 'TIPO',
  LINKED: 'LINKED',
  'NO TAGS': 'SEM TAGS',
  'HAS LINKS': 'TEM LINKS',
  'HAS IMAGES': 'TEM IMAGENS',
  INCLUDE: 'INCLUIR',
  EXCLUDE: 'EXCLUIR',
  TEXT: 'TEXTO',
  IS: 'É',
  ISNOT: 'NÃO É',
  SELECT: 'SELECCIONAR',
  'ADD FILTER TERMS': 'ADICIONAR TERMOS DE FILTRAGEM',
  FILTER: 'FILTRAR',
  TITLE: 'TÍTULO',
  'CREATE QUERY': 'CRIAR QUERY',
  'EDIT QUERY': 'EDITAR QUERY',
  MATCH: 'IGUALA',
  TIMES: 'VEZES',
  'Share Memo Image': 'Partilhar Imagem de Memo',
  '↗Click the button to save': '↗Clique no botão para guardar',
  'Image is generating...': 'A gerar Imagem..',
  'Image is loading...': 'A carregar Imagem...',
  'Loading...': 'Carregando...',
  '😟 Cannot load image, image link maybe broken':
    '😟 Não é possível carregar a imagem, o link da imagem pode estar incorrecto',
  'Daily Memos': 'Memos Diários',
  'CANCEL EDIT': 'CANCELAR EDIÇÃO',
  'LINK TO THE': 'LINK PARA O',
  'Mobile Options': 'Opções Móveis',
  "Don't support web image yet, please input image path in vault":
    'Ainda não existe suporte para imagens de web. Por favor, insira o link para uma imagem do vault',
  'Experimental Options': 'Opções Experimentais',
  'Background Image in Dark Theme': 'Imagem de Fundo no Tema Escuro',
  'Background Image in Light Theme': 'Imagem de Fundo no Tema Claro',
  'Set background image in dark theme. Set something like "Daily/one.png"':
    'Defina a imagem de fundo para o tema escuro. Defina da seguinte forma: "Daily/one.png".',
  'Set background image in light theme. Set something like "Daily/one.png"':
    'Defina a imagem de fundo para o tema claro. Defina da seguinte forma: "Daily/one.png".',
  'Set default memo composition, you should use {TIME} as "HH:mm" and {CONTENT} as content. "{TIME} {CONTENT}" by default':
    'Defina a composição padrão do memorando, deve usar {TIME} como "HH:mm" e {CONTENT} como conteúdo. Padrão: "{TIME} {CONTENT}".',
  'Default Memo Composition': 'Composição Padrão de um Memorando',
  'Show Tasks Label': 'Mostrar Etiquetas de Tarefas',
  'Show tasks label near the time text. False by default':
    'Mostrar etiquetas de tarefas próximas do texto de tempo. Padrão: "Falso".',
  'Please Open Memos First': 'Por favor, abra o Memos primeiro',
  DATE: 'DATA',
  OBSIDIAN_NLDATES_PLUGIN_NOT_ENABLED: 'OBSIDIAN_NLDATES_PLUGIN_NOT_ENABLED',
  BEFORE: 'ANTES',
  AFTER: 'DEPOIS',
  'Allow Comments On Memos': 'Permitir Comentários nos Memorandos',
  'You can comment on memos. False by default': 'Permite que comente os memorandos. Padrão: "Falso".',
  Import: 'Importar',
  'TITLE CANNOT BE NULL!': 'O TÍTULO NÃO PODE SER NULO!',
  'FILTER CANNOT BE NULL!': 'O FILTRO NÃO PODE SER NULO!',
  'Comments In Original DailyNotes/Notes': 'Comentários nas Notas/Notas Diárias Originais',
  'You should install Dataview Plug-in ver 0.5.9 or later to use this feature.':
    'Deve instalar a versão 0.5.9 ou posterior do plugin Dataview para usar esta funcionalidade.',
  'Open Memos Successfully': 'Memos Iniciado com Sucesso',
  'Fetch Error': '😭 Erro de Fetch',
  'Copied to clipboard Successfully': 'Copiado para a área de transferência com sucesso',
  'Check if you opened Daily Notes Plugin Or Periodic Notes Plugin':
    'Verifique se abriu o plugin de Notas Diárias ou de Notas Periódicas',
  'Please finish the last filter setting first': 'Por favor, termine  primeiro a configuração do último filtro',
  'Close Memos Successfully': 'Memos Fechado com Sucesso',
  'Insert as Memo': 'Inserir como um Memorando',
  'Insert file as memo content': 'Inserir ficheiro como conteúdo de um memorando',
  'Image load failed': 'Falha no carregamento da imagem',
  'Content cannot be empty': 'O Conteúdo não pode estar vazio',
  'Unable to create new file.': 'Não foi possível criar um novo ficheiro.',
  'Failed to fetch deleted memos: ': 'Falha no fetch dos memorandos removidos: ',
  'RESTORE SUCCEED': 'RESTAURO BEM SUCEDIDO',
  'Save Memo button icon': 'Ícone do Botão para Guardar Memorandos',
  'The icon shown on the save Memo button in the UI.': 'O ícone exibido na UI do botão para guardar memorandos.',
  'Fetch Memos From Particular Notes': 'Obter Memorandos de Notas Específicas',
  'You can set any Dataview Query for memos to fetch it. All memos in those notes will show on list. "#memo" by default':
    'Pode definir qualquer Query de Dataview para o Memos procurar. Todos os memorandos nessas notas serão mostrados na lista. Padrão: "#memo".',
  'Allow Memos to Fetch Memo from Notes': 'Permitir que o Memos Obtenha memorandos das Notas',
  'Use Memos to manage all memos in your notes, not only in daily notes. False by default':
    'Use o Memos para gerir todos os memorandos nas suas notas e não apenas nas notas diárias. Padrão: "Falso".',
  'Always Show Memo Comments': 'Mostrar Comentários dos Memorandos',
  'Always show memo comments on memos. False by default':
    'Mostrar sempre os comentários dos memorandos. Padrão: "Falso".',
  "You didn't set folder for daily notes in both periodic-notes and daily-notes plugins.":
    'Não definiu a pasta para as notas diárias, quer no plugin the Notas Periódicas ou de Notas Diárias.',
  'Please check your daily note plugin OR periodic notes plugin settings':
    'Por favor, verifique as configurações dos plugins de Notas Diárias OU de Notas Periódicas',
  "Use Which Plugin's Default Configuration": 'Usar a Configuração Padrão do Plugin',
  "Memos use the plugin's default configuration to fetch memos from daily, 'Daily' by default.":
    "O Memos usa a configuração padrão do plugin seleccionado para obter memorandos diariamente. Padrão: 'Notas Diárias'.",
  Daily: 'Diário',
};
