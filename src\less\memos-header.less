@import './mixin.less';

div[data-type='memos_view'] .section-header-container,
div[data-type='memos_view'] .memos-header-container {
  .flex(row, space-between, center);
  width: 100%;
  height: 40px;
  flex-wrap: nowrap;
  margin-top: 16px;
  // margin-bottom: 8px;
  flex-shrink: 0;

  > .title-text {
    .flex(row, flex-start, center);
    font-weight: bold;
    font-size: 18px;
    line-height: 40px;
    color: @text-black;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 0;
    cursor: pointer;

    > .action-btn {
      .flex(row, center, center);
      width: 24px;
      height: 24px;
      margin-right: 4px;
      flex-shrink: 0;
      background-color: unset;

      > .icon-img {
        width: 18px;
        height: 18px;
      }
    }
  }

  > .btns-container {
    .flex(row, flex-end, center);
  }
}

//@media only screen and (max-width: 875px) {
//  div[data-type='memos_view'] .section-header-container,
//  div[data-type='memos_view'] .memos-header-container {
//    height: auto;
//    margin-top: 4px;
//    margin-bottom: 0;
//    padding: 0 12px;
//    padding-bottom: 8px;
//    > .title-text {
//      > .action-btn {
//        .flex(row, center, center);
//        width: 60px;
//        height: 24px;
//        margin-right: -8px;
//        margin-left: -20px;
//        flex-shrink: 0;
//        background-color: unset;
//
//        > .icon-img {
//          width: 18px;
//          height: 18px;
//        }
//      }
//    }
//  }
//}

div[data-type='memos_view'].mobile-view .section-header-container,
div[data-type='memos_view'].mobile-view .memos-header-container {
  height: auto;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0 12px;
  padding-bottom: 8px;
  > .title-text {
    > .action-btn {
      .flex(row, center, center);
      width: 60px;
      height: 24px;
      margin-right: -8px;
      margin-left: -20px;
      flex-shrink: 0;
      background-color: unset;

      > .icon-img {
        width: 18px;
        height: 18px;
      }
    }
  }
}

.theme-dark div[data-type='memos_view'] .section-header-container,
.theme-dark div[data-type='memos_view'] .memos-header-container {
  .flex(row, space-between, center);
  width: 100%;
  height: 40px;
  flex-wrap: nowrap;
  margin-top: 16px;
  // margin-bottom: 8px;
  flex-shrink: 0;

  > .title-text {
    .flex(row, flex-start, center);
    font-weight: bold;
    font-size: 18px;
    line-height: 40px;
    color: @text-dark-black;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 0;
    cursor: pointer;

    > .action-btn {
      .flex(row, center, center);
      width: 24px;
      height: 24px;
      margin-right: 4px;
      flex-shrink: 0;
      background-color: unset;

      > .icon-img {
        width: 18px;
        height: 18px;

        fill: #cdcdcd;
      }
    }
  }

  > .btns-container {
    .flex(row, flex-end, center);
  }
}

//@media only screen and (max-width: 875px) {
//  .theme-dark div[data-type='memos_view'] .section-header-container,
//  .theme-dark div[data-type='memos_view'] .memos-header-container {
//    height: auto;
//    margin-top: 4px;
//    margin-bottom: 0;
//    padding: 0 12px;
//    padding-bottom: 8px;
//    > .title-text {
//      color: @text-dark-black;
//      > .action-btn {
//        .flex(row, center, center);
//        width: 60px;
//        height: 24px;
//        margin-right: -8px;
//        margin-left: -20px;
//        flex-shrink: 0;
//        background-color: unset;
//
//        > .icon-img {
//          width: 18px;
//          height: 18px;
//
//          filter: invert(0.8);
//        }
//      }
//    }
//  }
//}

.theme-dark div[data-type='memos_view'].mobile-view .section-header-container,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container {
  height: auto;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0 12px;
  padding-bottom: 8px;
  > .title-text {
    color: @text-dark-black;
    > .action-btn {
      .flex(row, center, center);
      width: 60px;
      height: 24px;
      margin-right: -8px;
      margin-left: -20px;
      flex-shrink: 0;
      background-color: unset;

      > .icon-img {
        width: 18px;
        height: 18px;

        fill: #cdcdcd;
      }
    }
  }
}
